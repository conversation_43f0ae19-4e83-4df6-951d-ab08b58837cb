import pygame
import random
from collections import namedtuple
import math

pygame.init()
checkered_bg = pygame.image.load("JumpyAI/checkered_bg.jpg")
coin_sprite = pygame.image.load("JumpyAI/coin.jpg")


# RGB Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
BLUE = (0, 0, 255)

# Game Constants
SPEED = 60

def check_collision(ax, ay, aw, ah, bx, by, bw, bh):
    return (ax + aw > bx) and (ax < bx + bw) and (ay + ah > by) and (ay < by + bh)

class Background:
    def __init__(self):
        self.sprite = checkered_bg
        self.y_pos = 0

class Reward:
    def __init__(self, x, y):
        self.sprite = coin_sprite
        self.position = pygame.Vector2(x, y)
        self.w = self.sprite.get_width()
        self.h = self.sprite.get_height()

class Barrier:
    def __init__(self, x, y, w, h, sprite):
        self.x = x
        self.y = y
        self.w = w
        self.h = h
        self.sprite = sprite

class Jumpy:
    def __init__(self, x=200, y=250):
        self.x = x
        self.y = y
        self.velocity = pygame.Vector2(0, 0)
        self.acceleration = pygame.Vector2(0, 0)
        self.jumps = 50
        self.jump_cooldown = 0
        self.jump_strength = 4
        self.x_speed = 4
        self.score = 0

class JumpyGame:
    def __init__(self, w=640, h=560):
        self.w = w
        self.h = h
        self.display = pygame.display.set_mode((self.w, self.h))
        pygame.display.set_caption("Jumpy Game")
        self.gravity = 0.005
        self.Jumpy = Jumpy()
        self.playerYPos = 360
        self.bg = [Background() for _ in range(3)]
        self.rewards = [Reward(random.randint(20, w-20), random.randint(20, h-20)) for _ in range(1)]
        self.ground = Barrier(0, h-10, w, 15, None)
        self.barriers = []
        self.clock = pygame.time.Clock()
        self.render = True
        # when we draw the player, use the playerYPos
        # Jumpy.y will be used for moving everything else relative to the player's position

    def play(self):
        # 1. Update forces
        camera_y = self.Jumpy.y - self.playerYPos
        camera_x = 0

        if self.Jumpy.y + 10 < self.ground.y:
            self.Jumpy.acceleration.y += self.gravity
            self.Jumpy.velocity.y += self.Jumpy.acceleration.y
        else:
            self.Jumpy.velocity.y *= -0.3
            self.Jumpy.acceleration.y = 0
            self.Jumpy.y = self.ground.y - 9

        # 2. Events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                quit
        keys = pygame.key.get_pressed()
        if keys[pygame.K_UP] and self.Jumpy.jumps > 0 and self.Jumpy.jump_cooldown <= 0:
            self.Jumpy.velocity.y = -1 * self.Jumpy.jump_strength
            self.Jumpy.acceleration.y = 0
            self.Jumpy.jumps -= 1
            self.Jumpy.jump_cooldown = 30
        if keys[pygame.K_LEFT]:
            self.Jumpy.velocity.x = -self.Jumpy.x_speed
        elif keys[pygame.K_RIGHT]:
            self.Jumpy.velocity.x = self.Jumpy.x_speed
        else:
            self.Jumpy.velocity.x = 0

        # 3. Update position
        if self.Jumpy.x + 10 > self.w:
            self.Jumpy.velocity.x = min(self.Jumpy.velocity.x, 0)
        elif self.Jumpy.x - 10 < 0:
            self.Jumpy.velocity.x = max(self.Jumpy.velocity.x, 0)

        self.Jumpy.x += self.Jumpy.velocity.x
        self.Jumpy.y += self.Jumpy.velocity.y
        if self.Jumpy.jump_cooldown > 0: self.Jumpy.jump_cooldown -= 1

        # 4. Check collisions
        for i in range(len(self.rewards)):
            reward = self.rewards[i]
            screen_x = int(reward.position.x - camera_x)
            screen_y = int(reward.position.y - camera_y)
            if check_collision(self.Jumpy.x - 10, self.playerYPos - 10, 20, 20, screen_x, screen_y, reward.w, reward.h):
                print("Collision with", reward.position)
                self.Jumpy.score += 1
                self.rewards[i] = Reward(random.randint(20, self.w-20), random.randint(20, self.h-20))
                self.Jumpy.jumps += 1

        # 5. Render
        if self.render:
            self._update_ui()
            self.clock.tick(SPEED)
        else:
            pygame.event.pump()

    def _update_ui(self):

        self.display.fill(BLACK)

        camera_y = self.Jumpy.y - self.playerYPos
        camera_x = 0

        # Draw Background
        tile_idx = round(self.Jumpy.y / self.h)
        for i in range(len(self.bg)):
            k = i-1
            self.bg[i].y_pos = (tile_idx + k) * self.h - camera_y
            self.display.blit(self.bg[i].sprite, (0, self.bg[i].y_pos))

        # Draw Barriers & Ground
        pygame.draw.rect(self.display, BLACK, (self.ground.x - camera_x, self.ground.y - camera_y, self.ground.w, self.ground.h))

        # Draw Rewards & Score
        for reward in self.rewards:
            screen_x = int(reward.position.x - camera_x)
            screen_y = int(reward.position.y - camera_y)
            self.display.blit(reward.sprite, (screen_x, screen_y))
        font = pygame.font.SysFont('comic sans', 20)
        text = font.render("Score: " + str(self.Jumpy.score), True, BLACK)
        self.display.blit(text, (10, 10))

        # Draw Jumpy
        #pygame.draw.circle(self.display, BLUE, (self.Jumpy.x, self.Jumpy.y), 10)
        pygame.draw.circle(self.display, BLUE, (self.Jumpy.x, self.playerYPos), 10)

        pygame.display.flip()

game = JumpyGame()
while True:
    game.play()


def main():

    x = 100
    y = 100
    radius = 10

    velocity = pygame.Vector2(2, 0)
    acceleration = pygame.Vector2(0, 0.125)

    display = pygame.display.set_mode((400, 400))
    pygame.display.set_caption("Flappy Bird")
    display.fill(WHITE)
    #pirate_ship = pygame.image.load("pirate_ship.png")

    while True:
        # the player's y position will be fixed, where everything else will move up and down for a constant camera view
        # 1. Events
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                quit()
        keys = pygame.key.get_pressed()
        if keys[pygame.K_ESCAPE]:
            pygame.quit()
            quit()
        if keys[pygame.K_w]:
            velocity.y = -3
        
        # 2. Update position
        velocity.x += acceleration.x
        velocity.y += acceleration.y 

        x += velocity.x
        y += velocity.y

        

        # Render
        display.fill(WHITE)
        pygame.draw.circle(display, BLUE, (x, y), 10)
        #display.blit(pirate_ship, (x, y))
        pygame.display.update()
        pygame.time.delay(16)

main()